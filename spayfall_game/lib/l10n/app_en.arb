{"appTitle": "SpyOut", "createRoom": "Create Room", "joinRoom": "Join Room", "roomCode": "Room Code", "playerName": "Player Name", "startGame": "Start Game", "waitingForPlayers": "Waiting for players...", "yourRole": "Your Role", "civilian": "Civilian", "spy": "Spy", "location": "Location", "timeRemaining": "Time Remaining", "accusePlayer": "Accuse Player", "guessLocation": "Guess Location", "vote": "Vote", "yes": "Yes", "no": "No", "gameOver": "Game Over", "winner": "Winner", "score": "Score", "nextRound": "Next Round", "newGame": "New Game", "settings": "Settings", "language": "Language", "english": "English", "turkish": "Turkish", "players": "Players", "round": "Round", "accusation": "Accusation", "voting": "Voting", "spyWins": "Spy Wins!", "civiliansWin": "Civilians Win!", "correctGuess": "Correct Guess!", "wrongGuess": "Wrong Guess!", "unanimousVote": "Unanimous Vote Required", "accusationFailed": "Accusation Failed", "timeUp": "Time's Up!", "askQuestion": "Ask a Question", "answer": "Answer", "question": "Question", "chat": "Cha<PERSON>", "send": "Send", "viewSpyLocations": "View Spy's Locations", "spyLocationsList": "Spy's Seen Locations", "noLocationsYet": "No locations seen yet", "ultimateSpyGame": "The Ultimate Spy Game", "hostInfo": "You will be the host of this room. Share the room code with other players to let them join.", "joinRoomInfo": "Ask the host for the room code. Room codes are 6 characters long.", "connectionError": "Connection error: {error}", "@connectionError": {"placeholders": {"error": {"type": "String"}}}, "goBack": "Go Back", "roomNotFound": "Room not found\\nRoom ID: {roomId}", "@roomNotFound": {"placeholders": {"roomId": {"type": "String"}}}, "roomCodeCopied": "Room code copied to clipboard", "host": "Host", "needMinPlayers": "Need at least 4 players to start", "failedToCreateRoom": "Failed to create room: {error}", "@failedToCreateRoom": {"placeholders": {"error": {"type": "String"}}}, "failedToJoinRoom": "Failed to join room: {error}", "@failedToJoinRoom": {"placeholders": {"error": {"type": "String"}}}, "failedToStartGame": "Failed to start game: {error}", "@failedToStartGame": {"placeholders": {"error": {"type": "String"}}}, "accusesText": " accuses ", "ofBeingSpyText": " of being the spy!", "timeRemainingLabel": "Time Remaining", "enterNameAndCode": "Enter your name and room code to join an existing game", "gameAlreadyStarted": "Game has already started", "roomIsFull": "Room is full", "roomNotFoundError": "Room not found", "viewAllLocations": "View All Locations", "accused": "ACCUSED", "pleaseEnterName": "Please enter your name", "enterNameToCreate": "Enter your name to create a new game room", "whereDoYouThink": "Where do you think you are?", "cancel": "Cancel", "changeLanguage": "Change Language", "allLocations": "All Locations", "close": "Close", "leaveGame": "Leave Game?", "leaveGameConfirm": "Are you sure you want to leave the game?", "leave": "Leave", "languageChanged": "Language changed successfully", "selectPlayerToAccuse": "Who do you think is the spy?", "youAreAccused": "You are accused and cannot vote", "failedToAccusePlayer": "Failed to accuse player: {error}", "@failedToAccusePlayer": {"placeholders": {"error": {"type": "String"}}}, "howToPlay": "How to Play", "howToPlayTitle": "How to Play SpyOut?", "gameObjective": "Game Objective", "gameObjectiveText": "SpyOut is a hidden identity and deduction game. Players are divided into two groups: Civilians and Spy.", "civilianObjective": "Civilian Objective", "civilianObjectiveText": "• Find and accuse the spy\n• Ask questions to expose the spy\n• Cooperate with other civilians", "spyObjective": "Spy Objective", "spyObjectiveText": "• Keep your identity hidden\n• Try to guess the location\n• Act like a civilian", "gameSetup": "Game Setup", "gameSetupText": "• Minimum 4, maximum 10 players\n• One player creates a room and shares the code\n• Other players join using the room code\n• Host starts the game", "gameFlow": "Game Flow", "gameFlowText": "• When the game starts, roles are secretly assigned\n• Civilians learn the location, spy doesn't\n• Players take turns asking questions and answering\n• Any player can make an accusation at any time", "questionPhase": "Question Phase", "questionPhaseText": "• Each player can ask questions to other players\n• Questions should be location-related but not too obvious\n• Spy must answer carefully since they don't know the location\n• Civilians should try to catch the spy", "accusationPhase": "Accusation Phase", "accusationPhaseText": "• Any player can make an accusation at any time\n• The accused player cannot vote\n• All other players vote\n• Decision must be unanimous", "winConditions": "Win Conditions", "winConditionsText": "• Civilians win: If they correctly accuse the spy\n• Spy wins: If they correctly guess the location\n• Spy wins: If they are wrongly accused\n• Spy wins: If time runs out", "tips": "Tips", "tipsText": "• Civilians: Don't ask obvious questions, don't give away the location\n• Civilians: Listen carefully to other players' answers\n• Spy: Ask smart questions to learn the location\n• Spy: Act natural to avoid suspicion\n• Everyone: Don't forget the time limit!", "ready": "Ready", "notReady": "Not Ready", "waitingForAllPlayers": "Waiting for all players to be ready...", "allPlayersReady": "All players are ready!"}
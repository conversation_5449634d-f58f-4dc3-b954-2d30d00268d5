import 'player.dart';
import 'location.dart';

class GameRoom {
  final String id;
  final String code;
  final String hostId;
  final List<Player> players;
  final GameState state;
  final Location? currentLocation;
  final List<String> spySeenLocations;
  final int currentRound;
  final int maxRounds;
  final int roundDurationMinutes;
  final DateTime? roundStartTime;
  final DateTime? roundEndTime;
  final String? currentAccuserId;
  final String? accusedPlayerId;
  final Map<String, bool> votes;
  final List<ChatMessage> chatMessages;
  final DateTime createdAt;
  final DateTime updatedAt;

  const GameRoom({
    required this.id,
    required this.code,
    required this.hostId,
    this.players = const [],
    this.state = GameState.waiting,
    this.currentLocation,
    this.spySeenLocations = const [],
    this.currentRound = 1,
    this.maxRounds = 3,
    this.roundDurationMinutes = 8,
    this.roundStartTime,
    this.roundEndTime,
    this.currentAccuserId,
    this.accusedPlayerId,
    this.votes = const {},
    this.chatMessages = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  GameRoom copyWith({
    String? id,
    String? code,
    String? hostId,
    List<Player>? players,
    GameState? state,
    Location? currentLocation,
    List<String>? spySeenLocations,
    int? currentRound,
    int? maxRounds,
    int? roundDurationMinutes,
    DateTime? roundStartTime,
    DateTime? roundEndTime,
    String? currentAccuserId,
    String? accusedPlayerId,
    Map<String, bool>? votes,
    List<ChatMessage>? chatMessages,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return GameRoom(
      id: id ?? this.id,
      code: code ?? this.code,
      hostId: hostId ?? this.hostId,
      players: players ?? this.players,
      state: state ?? this.state,
      currentLocation: currentLocation ?? this.currentLocation,
      spySeenLocations: spySeenLocations ?? this.spySeenLocations,
      currentRound: currentRound ?? this.currentRound,
      maxRounds: maxRounds ?? this.maxRounds,
      roundDurationMinutes: roundDurationMinutes ?? this.roundDurationMinutes,
      roundStartTime: roundStartTime ?? this.roundStartTime,
      roundEndTime: roundEndTime ?? this.roundEndTime,
      currentAccuserId: currentAccuserId ?? this.currentAccuserId,
      accusedPlayerId: accusedPlayerId ?? this.accusedPlayerId,
      votes: votes ?? this.votes,
      chatMessages: chatMessages ?? this.chatMessages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'hostId': hostId,
      'players': players.map((p) => p.toJson()).toList(),
      'state': state.toString(),
      'currentLocation': currentLocation?.toJson(),
      'spySeenLocations': spySeenLocations,
      'currentRound': currentRound,
      'maxRounds': maxRounds,
      'roundDurationMinutes': roundDurationMinutes,
      'roundStartTime': roundStartTime?.millisecondsSinceEpoch,
      'roundEndTime': roundEndTime?.millisecondsSinceEpoch,
      'currentAccuserId': currentAccuserId,
      'accusedPlayerId': accusedPlayerId,
      'votes': votes,
      'chatMessages': chatMessages.map((m) => m.toJson()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory GameRoom.fromJson(Map<String, dynamic> json) {
    return GameRoom(
      id: json['id'] ?? '',
      code: json['code'] ?? '',
      hostId: json['hostId'] ?? '',
      players: (json['players'] as List<dynamic>?)
          ?.map((p) => Player.fromJson(p as Map<String, dynamic>))
          .toList() ?? [],
      state: GameState.values.firstWhere(
        (e) => e.toString() == json['state'],
        orElse: () => GameState.waiting,
      ),
      currentLocation: json['currentLocation'] != null
          ? Location.fromJson(json['currentLocation'] as Map<String, dynamic>)
          : null,
      spySeenLocations: List<String>.from(json['spySeenLocations'] ?? []),
      currentRound: json['currentRound'] ?? 1,
      maxRounds: json['maxRounds'] ?? 3,
      roundDurationMinutes: json['roundDurationMinutes'] ?? 8,
      roundStartTime: json['roundStartTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['roundStartTime'])
          : null,
      roundEndTime: json['roundEndTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['roundEndTime'])
          : null,
      currentAccuserId: json['currentAccuserId'],
      accusedPlayerId: json['accusedPlayerId'],
      votes: Map<String, bool>.from(json['votes'] ?? {}),
      chatMessages: (json['chatMessages'] as List<dynamic>?)
          ?.map((m) => ChatMessage.fromJson(m as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updatedAt']),
    );
  }

  Player? get host => players.where((p) => p.id == hostId).isNotEmpty
      ? players.firstWhere((p) => p.id == hostId)
      : null;
  Player? get spy => players.where((p) => p.role == PlayerRole.spy).isNotEmpty
      ? players.where((p) => p.role == PlayerRole.spy).first
      : null;
  List<Player> get civilians => players.where((p) => p.role == PlayerRole.civilian).toList();
  
  bool get isGameActive => state == GameState.playing || state == GameState.voting;
  bool get canStartGame => players.length >= 4 && state == GameState.waiting && allPlayersReady;
  bool get allPlayersReady {
    if (players.isEmpty) return false;
    // Host otomatik olarak hazır sayılır, diğer oyuncuların hazır olması kontrol edilir
    return players.where((player) => !player.isHost).every((player) => player.isReady);
  }
  
  Duration? get timeRemaining {
    if (roundEndTime == null) return null;
    final now = DateTime.now();
    if (now.isAfter(roundEndTime!)) return Duration.zero;
    return roundEndTime!.difference(now);
  }
}

enum GameState {
  waiting,
  playing,
  voting,
  roundEnd,
  gameEnd,
}

class ChatMessage {
  final String id;
  final String playerId;
  final String playerName;
  final String message;
  final DateTime timestamp;
  final ChatMessageType type;

  const ChatMessage({
    required this.id,
    required this.playerId,
    required this.playerName,
    required this.message,
    required this.timestamp,
    this.type = ChatMessageType.normal,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'playerId': playerId,
      'playerName': playerName,
      'message': message,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'type': type.toString(),
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] ?? '',
      playerId: json['playerId'] ?? '',
      playerName: json['playerName'] ?? '',
      message: json['message'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
      type: ChatMessageType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => ChatMessageType.normal,
      ),
    );
  }
}

enum ChatMessageType {
  normal,
  system,
  accusation,
  vote,
}
